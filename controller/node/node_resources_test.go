package node

import (
	"fmt"
	"testing"
	"time"

	"github.com/matryer/is"

	"git.moderntv.eu/mcloud/system/common"
)

func TestNodeResources(t *testing.T) {
	// return // TODO: remove, currently crashes vscode
	t.Parallel()
	is := is.New(t)

	var now = time.Now()
	var startTime = now.Add(-10 * time.Minute)

	var dbTask1 = common.DbTask{
		DbId:              1,
		Id:                "task1",
		Action:            "run",
		RequiredResources: &common.Resources{"GPU": 500.0},
	}
	var dbTask2 = common.DbTask{
		DbId:              2,
		Id:                "task2",
		Action:            "run",
		RequiredResources: &common.Resources{"GPU": 300.0},
	}

	var sr = common.NewSelectedResources()
	sr["GPU"] = 1
	sr["disk"] = 0

	t.Run("basic", func(t *testing.T) {
		t.<PERSON>llel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})

	t.Run("starting node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:             "nodetask1",
			Task:           dbTask1.DbId,
			Node:           "node1",
			State:          "starting",
			StartTime:      &startTime,
			LastOnlineTime: &now,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:             "nodetask2",
			Task:           dbTask2.DbId,
			Node:           "node1",
			State:          "starting",
			LastOnlineTime: &now,
			StartTime:      &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 350.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("running node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("starting to running node task", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)

		dbNodeTask2 := common.DbNodeTask{
			Id:    "nodetask2",
			Task:  dbTask2.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)

		dbNodeTask1 = common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		dbNodeTask2 = common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 600.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("maximum task reservation reserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}}, nil)

		dbNodeTask1 := common.DbNodeTask{
			Id:               "nodetask1",
			Task:             dbTask1.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 1.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)

		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1.0, 1000.0})

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 1.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask2)

		//avail, _ = nr.GetAvailable("GPU")
		//is.Equal(avail, []float64{1000.0, 0.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		nr.RemoveTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})
	})
	t.Run("change free resources", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}}, nil)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}}, // out of 500
		}
		nr.AddTask(&dbNodeTask1)
		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			StartTime:        &startTime,
			RunningSinceTime: &startTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 50.0, 0.0}}, // out of 300
		}
		nr.AddTask(&dbNodeTask2)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 0.0, 1000.0})

		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, nil)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 350.0, 1000.0})
	})
	t.Run("node reservation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, map[string][]float64{"GPU": []float64{1000.0, 500.0, 1000.0}})
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		dbNodeTask1 := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask1.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask1,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": []float64{0.0, 100.0, 0.0}},
		}
		nr.AddTask(&dbNodeTask1)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 100.0, 1000.0})

		nr.RemoveTask(&dbNodeTask1)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 500.0, 1000.0})

		nr.UpdateResources(map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}}, map[string][]float64{"GPU": []float64{1000.0, 1000.0, 1000.0}})
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 1000.0, 1000.0})
	})
	t.Run("multiple updates", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		var nr = NewNodeResources()
		for i := 0; i < 100000; i++ {
			valNode := float64(((i * 5) % 500) + 10)
			valTaskX := float64((i*51)%1000) + 50
			valTaskY := float64((i*33)%1000) + 7

			t.Logf("updating node: free=%v, available=%v\n", map[string][]float64{"GPU": []float64{0, valNode + 50}}, map[string][]float64{"GPU": []float64{0, valNode}})
			nr.UpdateResources(map[string][]float64{"GPU": []float64{0, valNode + 50}}, map[string][]float64{"GPU": []float64{0, valNode}})

			var dbTaskX = common.DbTask{
				DbId:              1,
				Id:                "taskX",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": valTaskX},
			}
			dbNodeTaskX := common.DbNodeTask{
				Id:             "nodetaskX",
				Task:           dbTaskX.DbId,
				Node:           "node",
				State:          "starting",
				LastOnlineTime: &now,
				StartTime:      &startTime,
				OriginTask: common.RunnableTask{
					DbTask:            dbTaskX,
					SelectedResources: sr,
				},
				ResourceUsage: common.MultiResources{"GPU": []float64{0, valTaskX / 2}},
			}
			t.Logf("adding starting task: required=%f, used=%v\n", valTaskX, common.MultiResources{"GPU": []float64{0, valTaskX / 2}})
			nr.AddTask(&dbNodeTaskX)

			var dbTaskY = common.DbTask{
				DbId:              2,
				Id:                "taskY",
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": valTaskY},
			}
			dbNodeTaskY := common.DbNodeTask{
				Id:               "nodetaskY",
				Task:             dbTaskY.DbId,
				Node:             "node",
				State:            "running",
				LastOnlineTime:   &now,
				LastRunningTime:  &now,
				StartTime:        &startTime,
				RunningSinceTime: &startTime,
				OriginTask: common.RunnableTask{
					DbTask:            dbTaskY,
					SelectedResources: sr,
				},
				ResourceUsage: common.MultiResources{"GPU": []float64{0, valTaskY / 2}},
			}
			t.Logf("adding running task: required=%f, used=%v\n", valTaskX, common.MultiResources{"GPU": []float64{0, valTaskX / 2}})
			nr.AddTask(&dbNodeTaskY)

			avail, _ := nr.GetAvailable("GPU")
			expected := valNode - valTaskX/2 - valTaskY/2
			if expected < 0 {
				expected = 0
			}
			is.Equal(avail, []float64{0, expected})
		}
	})
	t.Run("add and remove resource index", func(t *testing.T) {
		//TODO
	})

	//TODO a task with more resources keys than node
}

// Additional comprehensive tests for edge cases and improved coverage
func TestNodeResourcesEdgeCases(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("empty resources", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()

		// Test with nil resources
		nr.UpdateResources(nil, nil)
		avail, info := nr.GetAvailable("GPU")
		is.Equal(avail, []float64(nil))
		is.Equal(info, map[string][]float64(nil))

		// Test with empty resources
		nr.UpdateResources(map[string][]float64{}, nil)
		avail, info = nr.GetAvailable("GPU")
		is.Equal(avail, []float64(nil))
		is.Equal(info, map[string][]float64(nil))
	})

	t.Run("zero resource requirements", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		// Task with zero resource requirement should be ignored
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 0.0},
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 0

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {0.0}},
		}

		nr.AddTask(&dbNodeTask)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0}) // Should remain unchanged
	})

	t.Run("nil required resources", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		// Task with nil required resources should be ignored
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: nil,
		}

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask: dbTask,
			},
		}

		nr.AddTask(&dbNodeTask)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0}) // Should remain unchanged
	})

	t.Run("resource dimension changes", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()

		// Start with 2 dimensions
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0, 2000.0}}, nil)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 2000.0})

		// Expand to 3 dimensions
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0, 2000.0, 3000.0}}, nil)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0, 2000.0, 3000.0})

		// Shrink to 1 dimension
		nr.UpdateResources(map[string][]float64{"GPU": {1500.0}}, nil)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1500.0})
	})

	t.Run("multiple resource types", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{
			"GPU":    {1000.0, 2000.0},
			"CPU":    {4000.0},
			"Memory": {8000.0, 16000.0, 32000.0},
		}, nil)

		// Verify all resource types are available
		gpuAvail, _ := nr.GetAvailable("GPU")
		is.Equal(gpuAvail, []float64{1000.0, 2000.0})

		cpuAvail, _ := nr.GetAvailable("CPU")
		is.Equal(cpuAvail, []float64{4000.0})

		memAvail, _ := nr.GetAvailable("Memory")
		is.Equal(memAvail, []float64{8000.0, 16000.0, 32000.0})
	})

	t.Run("task with unknown resource type", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		// Task requiring unknown resource type
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"UNKNOWN": 500.0},
		}

		sr := common.NewSelectedResources()
		sr["UNKNOWN"] = 0

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"UNKNOWN": {100.0}},
		}

		nr.AddTask(&dbNodeTask)

		// GPU should remain unchanged
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0})

		// Unknown resource should return nil
		unknownAvail, _ := nr.GetAvailable("UNKNOWN")
		is.Equal(unknownAvail, []float64(nil))
	})

	t.Run("boundary conditions", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		// Task requiring exactly all available resources
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 1000.0},
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 0

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {0.0}}, // No usage yet
		}

		nr.AddTask(&dbNodeTask)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{0.0}) // Should be exactly zero

		// Remove task and verify resources are restored
		nr.RemoveTask(&dbNodeTask)
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{1000.0})
	})

	t.Run("negative available resources", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {100.0}}, nil)

		// Task requiring more than available
		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 200.0},
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 0

		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {0.0}},
		}

		nr.AddTask(&dbNodeTask)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{0.0}) // Should be clamped to 0, not negative
	})

	t.Run("task update scenario", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		dbTask := common.DbTask{
			DbId:              1,
			Id:                "task1",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 500.0},
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 0

		// Add task first time
		dbNodeTask := common.DbNodeTask{
			Id:    "nodetask1",
			Task:  dbTask.DbId,
			Node:  "node1",
			State: "starting",
			OriginTask: common.RunnableTask{
				DbTask:            dbTask,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {100.0}},
		}

		nr.AddTask(&dbNodeTask)
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{600.0}) // 1000 - (500-100) = 600

		// Update task with different usage (AddTask should handle this)
		dbNodeTask.ResourceUsage = common.MultiResources{"GPU": {200.0}}
		nr.AddTask(&dbNodeTask) // This should remove old and add new

		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{700.0}) // 1000 - (500-200) = 700
	})

	t.Run("complex max reserve calculation", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		nr := NewNodeResources()
		nr.UpdateResources(map[string][]float64{"GPU": {1000.0}}, nil)

		var now = time.Now()
		var oldTime = now.Add(-30 * time.Second)

		// Add multiple non-reserved tasks with different reserves
		tasks := []struct {
			id       int
			required float64
			usage    float64
		}{
			{1, 300.0, 50.0},  // Reserve: 250
			{2, 400.0, 100.0}, // Reserve: 300 (max)
			{3, 200.0, 150.0}, // Reserve: 50
		}

		sr := common.NewSelectedResources()
		sr["GPU"] = 0

		for _, task := range tasks {
			dbTask := common.DbTask{
				DbId:              task.id,
				Id:                fmt.Sprintf("task%d", task.id),
				Action:            "run",
				RequiredResources: &common.Resources{"GPU": task.required},
			}

			dbNodeTask := common.DbNodeTask{
				Id:               fmt.Sprintf("nodetask%d", task.id),
				Task:             dbTask.DbId,
				Node:             "node1",
				State:            "running",
				LastOnlineTime:   &now,
				LastRunningTime:  &now,
				RunningSinceTime: &oldTime, // Old enough to not be reserved
				OriginTask: common.RunnableTask{
					DbTask:            dbTask,
					SelectedResources: sr,
				},
				ResourceUsage: common.MultiResources{"GPU": {task.usage}},
			}

			nr.AddTask(&dbNodeTask)
		}

		// Available should be total - max_reserve = 1000 - 300 = 700
		avail, _ := nr.GetAvailable("GPU")
		is.Equal(avail, []float64{700.0})

		// Remove the task with max reserve
		dbTask2 := common.DbTask{
			DbId:              2,
			Id:                "task2",
			Action:            "run",
			RequiredResources: &common.Resources{"GPU": 400.0},
		}

		dbNodeTask2 := common.DbNodeTask{
			Id:               "nodetask2",
			Task:             dbTask2.DbId,
			Node:             "node1",
			State:            "running",
			LastOnlineTime:   &now,
			LastRunningTime:  &now,
			RunningSinceTime: &oldTime,
			OriginTask: common.RunnableTask{
				DbTask:            dbTask2,
				SelectedResources: sr,
			},
			ResourceUsage: common.MultiResources{"GPU": {100.0}},
		}

		nr.RemoveTask(&dbNodeTask2)

		// Available should now be total - new_max_reserve = 1000 - 250 = 750
		avail, _ = nr.GetAvailable("GPU")
		is.Equal(avail, []float64{750.0})
	})
}
