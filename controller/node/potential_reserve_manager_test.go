package node

import (
	"testing"

	"github.com/matryer/is"
)

func TestPotentialReserveManager(t *testing.T) {
	t.Parallel()
	is := is.New(t)

	t.Run("NewPotentialReserveManager", func(t *testing.T) {
		t.<PERSON>llel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)

		is.True(prm != nil)
		is.True(prm.sliceManager == sm)
		is.True(prm.maxReserve != nil)
		is.True(prm.taskReserves != nil)
	})

	t.Run("InitializeResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)

		// Initialize new resource
		prm.InitializeResource("GPU", 4)

		is.True(prm.maxReserve["GPU"] != nil)
		is.Equal(len(prm.maxReserve["GPU"]), 4)

		// All values should be zero
		for i := 0; i < 4; i++ {
			is.Equal(prm.maxReserve["GPU"][i], 0.0)
		}

		// Initialize existing resource with different size
		prm.InitializeResource("GPU", 6)
		is.Equal(len(prm.maxReserve["GPU"]), 6)
	})

	t.Run("AddTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 4)

		// Add first task reserve
		reserves1 := []float64{10.0, 0.0, 20.0, 0.0}
		prm.AddTaskReserve(1, "GPU", reserves1)

		// Check max reserve (should match first task)
		maxReserve := prm.maxReserve["GPU"]
		is.Equal(maxReserve[0], 10.0)
		is.Equal(maxReserve[1], 0.0)
		is.Equal(maxReserve[2], 20.0)
		is.Equal(maxReserve[3], 0.0)

		// Check task reserves
		taskReserve := prm.taskReserves[1]["GPU"]
		is.True(taskReserve != nil)
		is.Equal(len(taskReserve), 4)
		is.Equal(taskReserve[0], 10.0)
		is.Equal(taskReserve[2], 20.0)

		// Add second task with higher reserves
		reserves2 := []float64{5.0, 25.0, 15.0, 30.0}
		prm.AddTaskReserve(2, "GPU", reserves2)

		// Check max reserve (should be maximum of both)
		is.Equal(maxReserve[0], 10.0) // max(10, 5)
		is.Equal(maxReserve[1], 25.0) // max(0, 25)
		is.Equal(maxReserve[2], 20.0) // max(20, 15)
		is.Equal(maxReserve[3], 30.0) // max(0, 30)
	})

	t.Run("AddTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 2)

		// Empty reserves should be ignored
		prm.AddTaskReserve(1, "GPU", []float64{})
		is.Equal(prm.maxReserve["GPU"][0], 0.0)
		is.Equal(prm.maxReserve["GPU"][1], 0.0)

		// Nil reserves should be ignored
		prm.AddTaskReserve(2, "GPU", nil)
		is.Equal(prm.maxReserve["GPU"][0], 0.0)
		is.Equal(prm.maxReserve["GPU"][1], 0.0)

		// Multiple resources for same task
		prm.AddTaskReserve(3, "GPU", []float64{10.0, 20.0})
		prm.AddTaskReserve(3, "CPU", []float64{5.0, 15.0})

		is.True(prm.taskReserves[3]["GPU"] != nil)
		is.True(prm.taskReserves[3]["CPU"] != nil)
	})

	t.Run("RemoveTaskReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 3)

		// Add multiple tasks
		prm.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prm.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prm.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Max should be [15, 25, 30]
		maxReserve := prm.maxReserve["GPU"]
		is.Equal(maxReserve[0], 15.0)
		is.Equal(maxReserve[1], 25.0)
		is.Equal(maxReserve[2], 30.0)

		// Remove task that doesn't have max reserve - should not need recalc
		needsRecalc := prm.RemoveTaskReserve(2, "GPU")
		is.Equal(needsRecalc, true) // Task 2 had max at index 1

		// Task should be removed
		is.Equal(prm.taskReserves[2], map[string][]float64(nil))

		// Remove task that has max reserve - should need recalc
		needsRecalc = prm.RemoveTaskReserve(1, "GPU")
		is.Equal(needsRecalc, true) // Task 1 had max at index 2
	})

	t.Run("RemoveTaskReserve edge cases", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 2)

		// Remove non-existent task
		needsRecalc := prm.RemoveTaskReserve(999, "GPU")
		is.Equal(needsRecalc, false)

		// Remove non-existent resource from existing task
		prm.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		needsRecalc = prm.RemoveTaskReserve(1, "CPU")
		is.Equal(needsRecalc, false)
	})

	t.Run("RecalculateMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 3)

		// Add tasks
		prm.AddTaskReserve(1, "GPU", []float64{10.0, 20.0, 30.0})
		prm.AddTaskReserve(2, "GPU", []float64{5.0, 25.0, 15.0})
		prm.AddTaskReserve(3, "GPU", []float64{15.0, 10.0, 20.0})

		// Manually corrupt max reserve
		prm.maxReserve["GPU"][0] = 999.0
		prm.maxReserve["GPU"][1] = 888.0
		prm.maxReserve["GPU"][2] = 777.0

		// Recalculate
		prm.RecalculateMaxReserveForResource("GPU")

		// Should be correct again
		maxReserve := prm.maxReserve["GPU"]
		is.Equal(maxReserve[0], 15.0) // max(10, 5, 15)
		is.Equal(maxReserve[1], 25.0) // max(20, 25, 10)
		is.Equal(maxReserve[2], 30.0) // max(30, 15, 20)

		// Recalculate non-existent resource
		prm.RecalculateMaxReserveForResource("NONEXISTENT")
		// Should not crash
	})

	t.Run("GetMaxReserve", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 2)
		prm.InitializeResource("CPU", 3)

		// Add some reserves
		prm.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})
		prm.AddTaskReserve(1, "CPU", []float64{5.0, 15.0, 25.0})

		result := prm.GetMaxReserve()

		// Should be a copy (test by modifying and checking original is unchanged)

		// Values should match
		is.Equal(len(result["GPU"]), 2)
		is.Equal(result["GPU"][0], 10.0)
		is.Equal(result["GPU"][1], 20.0)

		is.Equal(len(result["CPU"]), 3)
		is.Equal(result["CPU"][0], 5.0)
		is.Equal(result["CPU"][1], 15.0)
		is.Equal(result["CPU"][2], 25.0)

		// Modifying result should not affect original
		result["GPU"][0] = 999.0
		is.Equal(prm.maxReserve["GPU"][0], 10.0)
	})

	t.Run("GetMaxReserveForResource", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 2)

		prm.AddTaskReserve(1, "GPU", []float64{10.0, 20.0})

		result := prm.GetMaxReserveForResource("GPU")
		is.True(result != nil)
		is.Equal(len(result), 2)
		is.Equal(result[0], 10.0)
		is.Equal(result[1], 20.0)

		// Non-existent resource
		nilResult := prm.GetMaxReserveForResource("NONEXISTENT")
		is.Equal(nilResult, []float64(nil))
	})

	t.Run("complex scenario", func(t *testing.T) {
		t.Parallel()
		is := is.New(t)

		sm := NewSliceManager()
		prm := NewPotentialReserveManager(sm)
		prm.InitializeResource("GPU", 4)

		// Add multiple tasks with overlapping max reserves
		prm.AddTaskReserve(1, "GPU", []float64{100.0, 50.0, 75.0, 25.0})  // max at 0
		prm.AddTaskReserve(2, "GPU", []float64{80.0, 120.0, 60.0, 40.0})  // max at 1
		prm.AddTaskReserve(3, "GPU", []float64{90.0, 100.0, 150.0, 30.0}) // max at 2
		prm.AddTaskReserve(4, "GPU", []float64{70.0, 80.0, 90.0, 200.0})  // max at 3

		// Verify max reserves
		maxReserve := prm.maxReserve["GPU"]
		is.Equal(maxReserve[0], 100.0)
		is.Equal(maxReserve[1], 120.0)
		is.Equal(maxReserve[2], 150.0)
		is.Equal(maxReserve[3], 200.0)

		// Remove task with max at index 1
		needsRecalc := prm.RemoveTaskReserve(2, "GPU")
		is.Equal(needsRecalc, true)

		// Recalculate
		prm.RecalculateMaxReserveForResource("GPU")

		// Max at index 1 should now be 100 (from task 3)
		is.Equal(maxReserve[0], 100.0) // unchanged
		is.Equal(maxReserve[1], 100.0) // changed from 120 to 100
		is.Equal(maxReserve[2], 150.0) // unchanged
		is.Equal(maxReserve[3], 200.0) // unchanged
	})
}
